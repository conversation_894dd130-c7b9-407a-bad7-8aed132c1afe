import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const evaluationCriteria = {
  "1": {
    suspiciousElements: [
      "The account answers immediately after our messages",
      "That account isn't followed by anyone I know",
      "They never turned on their during the cal",
    ],
    trustElements: [
      "The account was verified (blue checkmark)",
      "It had a large follower count",
      "Its timeline was filled with crypto-related posts",
      "They mentioned they'd seen your talks at a crypto conference",
    ],
    worstCaseScenarios: [
      "Had your laptop compromised",
      "Exposed any cleartext data (passwords, seed phrases)",
      "Lost access to both online and offline accounts (including your Twitter account)",
      "Had your wallets drained",
    ],
    securityActions: [
      "Disconnect your laptop from the internet",
      "Factory-reset your machine",
      "Contact your incident response team or teams like SEAL911",
      "Check all open sessions on your online accounts",
      "Change every password on all accounts",
    ],
    preventiveMeasures: [
      "Refuse to share my Zoom screen",
      "Don't respond or click any links from that account",
      "Report the account to Twitter",
      "Alert a security specialist",
      "Share the suspicious profile with colleagues and contacts in the ecosystem",
    ],
    pointValues: {
      suspiciousElements: 20, // 5 points per item
      trustElements: 20, // 5 points per item
      worstCaseScenarios: 20, // 5 points per item
      securityActions: 25, // 5 points per item
      preventiveMeasures: 20, // 4 points per item
    },
  },
};

interface EvaluationResult {
  evaluation: CriterionMatch[];
}

interface CriterionMatch {
  criterion: string;
  p: number;
}

function getGameCriteria(gameId: string) {
  if (!evaluationCriteria[gameId as keyof typeof evaluationCriteria]) {
    return null;
  }
  return evaluationCriteria[gameId as keyof typeof evaluationCriteria];
}

function getAllCriteriaItems(
  criteria: any
): Array<{ item: string; points: number; category: string }> {
  return [
    ...criteria.suspiciousElements.map((item: string) => ({
      item,
      points: 5,
      category: "suspiciousElements",
    })),
    ...criteria.trustElements.map((item: string) => ({
      item,
      points: 5,
      category: "trustElements",
    })),
    ...criteria.worstCaseScenarios.map((item: string) => ({
      item,
      points: 5,
      category: "worstCaseScenarios",
    })),
    ...criteria.securityActions.map((item: string) => ({
      item,
      points: 5,
      category: "securityActions",
    })),
    ...criteria.preventiveMeasures.map((item: string) => ({
      item,
      points: 4,
      category: "preventiveMeasures",
    })),
  ];
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split("/");
    const gameId = pathParts[pathParts.length - 1];

    const { answer } = await request.json();

    const criteria = getGameCriteria(gameId);
    if (!criteria) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    // Flatten all criteria items into a single array
    const allCriteriaItems = getAllCriteriaItems(criteria);
    const allCriteriaTexts = allCriteriaItems.map((item) => item.item);

    // Create a direct evaluation request using o4-mini model
    const evaluationResponse = await openai.chat.completions.create({
      model: "o4-mini",
      messages: [
        {
          role: "system",
          content: `You are an expert evaluator for a cybersecurity awareness game. Your task is to analyze the player's answer and determine which criteria they have correctly identified.
          
          IMPORTANT EVALUATION RULES:
          1. Be EXTREMELY STRICT in your evaluation. Only match criteria when the player CLEARLY and EXPLICITLY mentions them.
          2. Do NOT match criteria for vague implications or general concepts.
          3. The player must demonstrate clear understanding of the specific criterion to be matched.
          4. If you're unsure whether to match a criterion, DO NOT match it.
          5. The player's answer must contain the key concepts and meaning of the criterion, not necessarily the exact wording.
          6. ONLY match criteria that are ACTUALLY MENTIONED in the player's answer.
          7. If the player's answer is very short or vague, be especially strict.
          
          For each criterion, assign a certainty score p between 0 and 1, where:
          - 1.0 means the criterion is definitely present in the player's answer
          - 0.0 means the criterion is definitely not present
          
          Only consider a criterion matched if p > 0.8.
          
          Return your evaluation as a JSON array of objects with this format:

          evaluation: [
            { "criterion": "criterion text", "p": 0.95 },
            { "criterion": "another criterion", "p": 0.2 }
          ]`,
        },
        {
          role: "user",
          content: `Player's answer: "${answer}"\n\nCriteria to evaluate:\n${allCriteriaTexts
            .map((c, i) => `${i + 1}. ${c}`)
            .join("\n")}`,
        },
      ],
      response_format: { type: "json_object" },
    });

    console.log("evaluationResponse", evaluationResponse);

    // Extract the evaluation results
    const evaluationText = evaluationResponse.choices[0].message.content;
    if (!evaluationText) {
      throw new Error("Failed to get evaluation from OpenAI");
    }

    // Parse the evaluation results
    const toolResults = JSON.parse(evaluationText) as EvaluationResult;

    console.log("Tool results:", JSON.stringify(toolResults));

    // Filter results to only include criteria with p > 0.8
    const matchedCriteria = toolResults?.evaluation?.filter(
      (result: CriterionMatch) => result.p > 0.8
    );
    console.log("Matched criteria:", JSON.stringify(matchedCriteria));

    // Calculate total points and format correct answers
    let totalPoints = 0;
    const correctAnswers: string[] = [];

    matchedCriteria.forEach((match: CriterionMatch) => {
      const criteriaItem = allCriteriaItems.find(
        (item) => item.item === match.criterion
      );
      if (criteriaItem) {
        totalPoints += criteriaItem.points;
        correctAnswers.push(
          `${criteriaItem.item} (${criteriaItem.points} points)`
        );
      }
    });

    const result = {
      totalPoints: totalPoints > 100 ? 100 : totalPoints,
      correctAnswers,
    };

    console.log(`Final evaluation: ${JSON.stringify(result)}`);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error("Error evaluating answer:", error);

    // Handle OpenAI API errors specifically
    if (error.name === "OpenAIError" || error.message?.includes("OpenAI")) {
      return NextResponse.json(
        {
          error: "OpenAI API Error",
          message:
            error.message ??
            "There was an issue with the AI service. Please try again later.",
          code: error.code ?? "unknown_error",
        },
        { status: 502 }
      );
    }

    return NextResponse.json(
      {
        error: "Failed to evaluate answer",
        message: error.message ?? "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
