import React, { useEffect, useState } from "react";
import gsap from "gsap";
import { useIsClient } from "../hooks/useIsClient";
import GlitchText from "../components/GlitchText/GlitchText";

const AppPreLoader = () => {
  const isClient = useIsClient();
  const [loadingProgress, setLoadingProgress] = useState(0);

  useEffect(() => {
    if (!isClient) return;

    const progressInterval = setInterval(() => {
      setLoadingProgress((prev) => {
        const newProgress = prev + Math.random() * 10; // 2x faster progress increment
        return newProgress >= 100 ? 100 : newProgress;
      });
    }, 75); // 2x faster interval

    const tl = gsap.timeline();

    const preLoaderAnim = () => {
      // Set initial states to prevent flash
      tl.set(".preloader", { opacity: 1 })
        .set(".matrix-bg", { opacity: 0 })
        .set(".rabbit-container", { opacity: 0 })
        .set(".follow-text", { opacity: 0 })
        .set(".zerotrust-text", { opacity: 0 })
        .set(".progress-bar-container", { opacity: 0 })
        .set(".progress-text", { opacity: 0 });

      tl.to(".matrix-bg", {
        duration: 1, // 2x faster
        opacity: 0.2,
        ease: "power2.inOut",
      })
        .to(".progress-bar-container", {
          duration: 0.4, // 2x faster
          opacity: 1,
          ease: "power2.inOut",
        })
        .to(
          ".progress-text",
          {
            duration: 0.4, // 2x faster
            opacity: 1,
            ease: "power2.inOut",
          },
          "-=0.2" // 2x faster offset
        )
        .to(
          ".rabbit-container",
          {
            duration: 0.75, // 2x faster
            opacity: 1,
            scale: 1.1,
            ease: "elastic.out(1, 0.3)",
          },
          "-=0.25" // 2x faster offset
        )
        .to(
          ".follow-text",
          {
            duration: 0.6, // 2x faster
            opacity: 1,
            y: -20,
            ease: "power3.out",
          },
          "-=0.4" // 2x faster offset
        )
        .to(
          ".zerotrust-text",
          {
            duration: 0.75, // 2x faster
            opacity: 1,
            scale: 1,
            ease: "power4.out",
          },
          "-=0.25" // 2x faster offset
        );

      const checkProgress = () => {
        if (loadingProgress >= 100) {
          clearInterval(progressCheckInterval);

          tl.to(
            ".follow-text, .zerotrust-text, .progress-bar-container, .progress-text",
            {
              duration: 0.35, // 2x faster
              opacity: 0,
              y: -50,
              stagger: 0.05, // 2x faster stagger
              ease: "power2.in",
            }
          )
            .to(
              ".rabbit-container",
              {
                duration: 0.4, // 2x faster
                opacity: 0,
                scale: 1.5,
                ease: "power2.in",
              },
              "-=0.2" // 2x faster offset
            )
            .to(
              ".matrix-bg",
              {
                duration: 0.5, // 2x faster
                opacity: 0,
                ease: "power2.inOut",
              },
              "-=0.3" // 2x faster offset
            )
            .to(
              ".preloader",
              {
                duration: 0.4, // 2x faster
                opacity: 0,
                ease: "power2.inOut",
                onComplete: () => {
                  if (document) {
                    document.body.style.overflow = "auto";
                  }
                },
              },
              "-=0.2" // 2x faster offset
            )
            .set(".preloader", {
              display: "none",
            });
        }
      };

      const progressCheckInterval = setInterval(checkProgress, 50); // 2x faster check
    };

    preLoaderAnim();

    return () => {
      clearInterval(progressInterval);
    };
  }, [loadingProgress, isClient]);

  // Generate matrix rain effect characters
  const generateMatrixChars = () => {
    const chars =
      "アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン0123456789";
    return Array.from({ length: 200 }, () => ({
      char: chars[Math.floor(Math.random() * chars.length)],
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 1.5 + 0.9, // Slightly larger
      opacity: Math.random() * 0.4 + 0.6, // Higher minimum opacity
      speed: Math.random() * 1.5 + 0.5, // 2x faster matrix animation
    }));
  };

  if (!isClient) {
    return null;
  }

  const matrixChars = generateMatrixChars();

  return (
    <div
      className="preloader overflow-hidden"
      style={{
        height: "100vh",
        width: "100%",
        background: "#000000",
        color: "#e5ebf2",
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 55,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        overflow: "hidden !important",
        opacity: 0, // Set initial opacity to 0
      }}
    >
      <style
        dangerouslySetInnerHTML={{
          __html: `
        @keyframes matrixRain {
          0% {
            transform: translateY(-10px);
            opacity: 0;
          }
          10% {
            opacity: var(--char-opacity);
          }
          90% {
            opacity: var(--char-opacity);
          }
          100% {
            transform: translateY(calc(100vh + 20px));
            opacity: 0;
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 1;
          }
          50% {
            transform: scale(1.05);
            opacity: 0.9;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-15px);
          }
        }

        .matrix-bg {
          opacity: 0;
        }

        .matrix-char {
          position: absolute;
          color: #00ff41;
          font-family: monospace;
          animation: matrixRain var(--fall-duration) linear infinite;
          animation-delay: var(--fall-delay);
          opacity: var(--char-opacity);
          text-shadow: 0 0 10px rgba(0, 255, 65, 1), 0 0 15px rgba(0, 255, 65, 0.8);
          font-weight: bold;
        }

        .zerotrust-text {
          position: relative;
          font-size: 4rem;
          font-weight: bold;
          text-transform: uppercase;
          color: white;
          letter-spacing: 0.1em;
          opacity: 0;
          transform: scale(0.9);
          text-shadow: 0 0 10px rgba(255, 255, 255, 0.7);
        }

        .follow-text {
          font-size: 1.5rem;
          font-weight: bold;
          color: #e4ded7;
          text-transform: uppercase;
          letter-spacing: 0.15em;
          opacity: 0;
          animation: float 3s ease-in-out infinite; /* 2x faster */
        }

        .rabbit-container {
          animation: pulse 1.5s ease-in-out infinite; /* 2x faster */
          opacity: 0;
        }

        .progress-bar {
          height: 4px;
          background: linear-gradient(90deg, #00ff41, #0088ff);
          transition: width 0.3s ease-out;
          box-shadow: 0 0 10px rgba(0, 255, 65, 0.7);
        }

        .progress-text {
          font-family: monospace;
          color: #00ff41;
          text-shadow: 0 0 5px rgba(0, 255, 65, 0.7);
        }
      `,
        }}
      />

      <div className="matrix-bg absolute inset-0 overflow-hidden opacity-0">
        {matrixChars.map((item, index) => (
          <div
            key={index}
            className="matrix-char"
            style={{
              left: `${item.x}%`,
              top: `${item.y}%`,
              fontSize: `${item.size}rem`,
              // @ts-expect-error note this is a custom CSS property
              "--char-opacity": item.opacity,
              "--fall-duration": `${item.speed}s`,
              "--fall-delay": `${Math.random() * 5}s`,
            }}
          >
            {item.char}
          </div>
        ))}
      </div>

      <div className="flex flex-col items-center justify-center gap-12 z-10 w-full px-4">
        <div className="follow-text text-center">Interactive Game</div>
        <div className="text-[16px] text-center">
          <GlitchText
            speed={0.5}
            className="text-[16px] uppercase tracking-wider font-bold"
          >
            ZeroTrust
          </GlitchText>
        </div>

        <div className="progress-bar-container w-full max-w-md h-1 bg-gray-800 mt-8 rounded-full overflow-hidden">
          <div
            className="progress-bar h-full"
            style={{ width: `${loadingProgress}%` }}
          ></div>
        </div>
        <div className="progress-text text-sm">
          Loading: {Math.floor(loadingProgress)}%
        </div>
      </div>
    </div>
  );
};

export default AppPreLoader;
