"use client";

import type React from "react";
import { useEffect, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  CheckCircle2,
  ChevronRight,
  Download,
  Layers,
  Share2,
  <PERSON>rkles,
  AudioWaveformIcon as Waveform,
  HelpCircle,
  ArrowRight,
} from "lucide-react";
import { useGameQuestions } from "../first-game/first-game.context";

type IconButtonProps = {
  icon: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
};

const IconButton = ({
  icon,
  onClick,
  disabled,
  className,
}: IconButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={disabled}
    className={cn(
      "p-1 rounded",
      disabled ? "cursor-not-allowed" : "hover:bg-gray-700",
      className
    )}
  >
    {icon}
  </button>
);

const HeaderIcons = ({ icons }: { icons: React.ReactNode[] }) => (
  <div className="flex space-x-3 text-gray-400">
    {icons.map((icon, index) => {
      const iconType = (icon as any)?.type?.name || `icon-${index}`;
      return <IconButton key={`header-${iconType}`} icon={icon} />;
    })}
  </div>
);

const TypingAnimation = () => {
  const [dots, setDots] = useState("");

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "" : prev + "."));
    }, 400);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="pl-2">
      <div className="prose prose-invert prose-sm max-w-none">
        <p className="flex items-center">
          Evaluating your answer
          <span className="inline-flex ml-1 min-w-[30px] font-bold text-xl leading-tight tracking-wider">
            {dots}
          </span>
        </p>
      </div>
    </div>
  );
};

export default function ChatGPTFrame() {
  const [inputValue, setInputValue] = useState("");
  const [currentHint, setCurrentHint] = useState<string | null>(null);
  const [showHint, setShowHint] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const {
    gameQuestions,
    currentQuestionIndex,
    questionResults,
    isLoadingQuestions,
    isSubmittingQuestionAnswer,
    loadQuestions,
    submitQuestionAnswer,
    getHint,
    nextQuestion,
    isGameCompleted,
    finalScore,
  } = useGameQuestions();

  // Load questions on mount
  useEffect(() => {
    if (!gameQuestions && !isLoadingQuestions) {
      loadQuestions();
    }
  }, [gameQuestions, isLoadingQuestions, loadQuestions]);

  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [currentQuestionIndex, questionResults]);

  const currentQuestion = gameQuestions?.questions[currentQuestionIndex];
  const currentResult = questionResults[currentQuestionIndex];
  const isInputDisabled = isSubmittingQuestionAnswer;
  const isButtonDisabled = !inputValue.trim() || isInputDisabled;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isButtonDisabled || !currentQuestion) return;

    const userAnswer = inputValue.trim();
    setInputValue("");
    setShowHint(false);
    setCurrentHint(null);

    try {
      await submitQuestionAnswer(currentQuestion.id, userAnswer);
    } catch (error) {
      console.error("Error submitting answer:", error);
    }
  };

  const handleGetHint = async () => {
    if (!currentQuestion) return;

    try {
      const hint = await getHint(currentQuestion.id);
      setCurrentHint(hint);
      setShowHint(true);
    } catch (error) {
      console.error("Error getting hint:", error);
    }
  };

  const handleNextQuestion = () => {
    nextQuestion();
    setShowHint(false);
    setCurrentHint(null);
  };

  if (isLoadingQuestions) {
    return (
      <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
        <div className="flex items-center px-3 py-2 bg-[#1e1e1e] border-b border-gray-700">
          <HeaderIcons
            icons={[
              <Layers size={18} key="layers" />,
              <Sparkles size={18} key="sp" />,
            ]}
          />
          <div className="flex items-center text-white font-medium ml-4">
            ChatGPT
            <ChevronRight size={20} className="ml-1" />
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center text-white">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
            <p>Loading questions...</p>
          </div>
        </div>
      </div>
    );
  }

  if (isGameCompleted) {
    return (
      <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
        <div className="flex items-center px-3 py-2 bg-[#1e1e1e] border-b border-gray-700">
          <HeaderIcons
            icons={[
              <Layers size={18} key="layers" />,
              <Sparkles size={18} key="sp" />,
            ]}
          />
          <div className="flex items-center text-white font-medium ml-4">
            ChatGPT
            <ChevronRight size={20} className="ml-1" />
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-6 text-white">
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold mb-4">🎉 Game Completed!</h2>
            <div className="text-5xl font-bold text-green-400 mb-2">
              {finalScore}
            </div>
            <div className="text-xl text-gray-300">
              out of {gameQuestions?.totalMaxPoints} points
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold mb-4">📊 Your Results:</h3>
            {questionResults.map((result, index) => (
              <div
                key={result.questionId}
                className="bg-[#40414f] rounded-lg p-4"
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold">Question {index + 1}</h4>
                  <span className="text-green-400 font-bold">
                    {result.totalPoints}/{result.maxPoints} pts
                  </span>
                </div>
                <p className="text-gray-300 text-sm mb-3">{result.question}</p>
                {result.correctAnswers.length > 0 && (
                  <div>
                    <p className="text-green-400 text-sm font-medium mb-1">
                      ✅ You identified:
                    </p>
                    <ul className="text-sm text-gray-300 space-y-1">
                      {result.correctAnswers.map((answer) => (
                        <li key={answer} className="pl-2">
                          • {answer}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>

          {finalScore >= 85 && (
            <div className="mt-6 text-center">
              <Button
                onClick={() => {
                  const text = `I scored ${finalScore}% on the Zero Trust Game!\n\nPlay this interactive experience to detect social engineering attacks: https://zero-trust-game.xyz\nBy @theSouilos & @0xmrudenko`;
                  window.open(
                    `https://twitter.com/intent/tweet?text=${encodeURIComponent(
                      text
                    )}`,
                    "_blank"
                  );
                }}
                className="bg-[#1d9bf0] hover:bg-[#1a8cd8] text-white"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share on Twitter
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
      <div className="flex items-center px-3 py-2 bg-[#1e1e1e] border-b border-gray-700">
        <HeaderIcons
          icons={[
            <Layers size={18} key="layers" />,
            <Sparkles size={18} key="sp" />,
          ]}
        />
        <div className="flex items-center text-white font-medium ml-4">
          ChatGPT
          <ChevronRight size={20} className="ml-1" />
        </div>
        <div className="ml-auto">
          <HeaderIcons
            icons={[
              <Download size={18} key="download" />,
              <Layers size={18} key="layers" />,
            ]}
          />
        </div>
      </div>

      <div ref={contentRef} className="flex-1 overflow-y-auto p-4 text-white">
        {/* Progress indicator */}
        <div className="mb-6 p-4 bg-[#40414f] rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-300">Progress</span>
            <span className="text-sm text-gray-300">
              Question {currentQuestionIndex + 1} of{" "}
              {gameQuestions?.totalQuestions}
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${
                  ((currentQuestionIndex + 1) /
                    (gameQuestions?.totalQuestions || 1)) *
                  100
                }%`,
              }}
            ></div>
          </div>
        </div>

        {/* Current question */}
        {currentQuestion && (
          <div className="mb-6">
            <div className="mb-4">
              <div className="font-semibold mb-2">ChatGPT:</div>
              <div className="pl-2">
                <div className="prose prose-invert prose-sm max-w-none">
                  <h3 className="text-lg font-semibold text-blue-400 mb-3">
                    Question {currentQuestionIndex + 1}
                  </h3>
                  <p className="text-white mb-4">{currentQuestion.question}</p>
                  <p className="text-sm text-gray-400">
                    Expected answers: {currentQuestion.expectedAnswersCount} |
                    Max points: {currentQuestion.maxPoints}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Show hint if requested */}
        {showHint && currentHint && (
          <div className="mb-4 p-3 bg-yellow-900/30 border border-yellow-600/50 rounded-lg">
            <div className="flex items-start gap-2">
              <HelpCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-semibold text-yellow-400 mb-1">Hint:</div>
                <div className="text-yellow-100">{currentHint}</div>
              </div>
            </div>
          </div>
        )}

        {/* Show result if answered */}
        {currentResult && (
          <div className="mb-4">
            <div className="font-semibold mb-2">ChatGPT:</div>
            <div className="pl-2">
              <div className="bg-[#40414f] rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <CheckCircle2 className="w-5 h-5 text-green-400" />
                  <span className="font-semibold text-green-400">
                    You scored {currentResult.totalPoints} out of{" "}
                    {currentResult.maxPoints} points!
                  </span>
                </div>

                {currentResult.correctAnswers.length > 0 && (
                  <div>
                    <p className="font-medium text-green-400 mb-2">
                      ✅ Correctly identified:
                    </p>
                    <ul className="space-y-1 text-sm">
                      {currentResult.correctAnswers.map((answer, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <span className="text-green-400 mt-1">•</span>
                          <span>{answer}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {currentQuestionIndex <
                  (gameQuestions?.totalQuestions || 0) - 1 && (
                  <div className="mt-4 pt-3 border-t border-gray-600">
                    <Button
                      onClick={handleNextQuestion}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Next Question
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Loading state */}
        {isSubmittingQuestionAnswer && (
          <div className="mb-4">
            <div className="font-semibold mb-2">ChatGPT:</div>
            <TypingAnimation />
          </div>
        )}
      </div>

      {/* Input area - only show if current question hasn't been answered */}
      {currentQuestion && !currentResult && (
        <div className="p-3 bg-[#343541] border-t border-gray-700">
          <div className="flex gap-2 mb-3">
            <Button
              onClick={handleGetHint}
              variant="outline"
              size="sm"
              className="bg-yellow-600/20 border-yellow-600/50 text-yellow-400 hover:bg-yellow-600/30"
            >
              <HelpCircle className="w-4 h-4 mr-1" />
              Hint
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="relative">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              disabled={isInputDisabled}
              placeholder="Type your answer..."
              className={cn(
                "w-full py-3 px-4 pr-16 rounded-lg text-white placeholder-gray-400 outline-none",
                isInputDisabled
                  ? "bg-[#2a2b36] cursor-not-allowed"
                  : "bg-[#40414f]"
              )}
            />
            <div className="absolute bottom-0 right-0 flex items-center h-full px-3 space-x-2 text-gray-400">
              <button
                type="submit"
                className={cn(
                  "p-1 rounded",
                  isButtonDisabled
                    ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                    : "bg-white/10 hover:bg-gray-600 text-white cursor-pointer"
                )}
                disabled={isButtonDisabled}
              >
                <Waveform size={18} />
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
