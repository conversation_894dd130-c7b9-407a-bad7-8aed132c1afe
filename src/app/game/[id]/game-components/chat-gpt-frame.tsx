/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import type React from "react";
import { useEffect, useRef, useState } from "react";
import ReactMarkdown from "react-markdown";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  CheckCircle2,
  ChevronRight,
  Download,
  Layers,
  Share2,
  Sparkles,
  AudioWaveformIcon as Waveform,
} from "lucide-react";
import { useGameAnswer } from "../first-game/first-game.context";

type IconButtonProps = {
  icon: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
};

type Message = {
  role: string;
  content: string;
};

const INITIAL_MESSAGE: Message = {
  role: "assistant",
  content: `## 🔍 Security Assessment Questions

### 🚩 Suspicious Elements
What suspicious things did you notice in this interaction?

### 🎭 Trust Building Tactics
Which tactics were used to build trust and appear legitimate?

### 💥 Potential Impact
What's the main impact here and what could you lose? Is your Twitter account infected? List everything that could have been compromised if you'd fallen for it.

### 🚨 Emergency Response
What direct actions must you take as an emergency response?

### 🛡️ Future Prevention
What would you do next time you see a similar situation?`,
};

const createTwitterShareUrl = (score: number) => {
  const text = `I have scored ${score}% on the first edition of the Zero Trust Game!\n\nPlay this interactive experience to detect new social engineering attacks: https://zero-trust-game.xyz\nBy @theSouilos & @0xmrudenko`;
  return `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}`;
};

const formatEvaluationContent = (
  totalPoints: number,
  correctAnswers: string[]
) => {
  // Ensure correctAnswers is an array before mapping
  const answersArray = Array.isArray(correctAnswers) ? correctAnswers : [];

  const formattedAnswers = answersArray
    .map((answer) => {
      // Handle the case where answer might be an object instead of a string
      if (typeof answer !== "string") {
        console.error("Unexpected answer format:", answer);
        return null;
      }

      const match = answer.match(/(.*?)\s*\((\d+) points\)$/);
      if (match) {
        const [_, itemText, points] = match;
        return `* ${itemText.trim()} (${points} points)`;
      }
      return `* ${answer.trim()}`;
    })
    .filter(Boolean) // Remove null entries
    .join("\n");

  return `
## 🎯 You scored ${totalPoints} points out of 100!

### ✅ Correctly identified items:
${formattedAnswers}
  `.trim();
};

const IconButton = ({
  icon,
  onClick,
  disabled,
  className,
}: IconButtonProps) => (
  <button
    type="button"
    onClick={onClick}
    disabled={disabled}
    className={cn(
      "p-1 rounded",
      disabled ? "cursor-not-allowed" : "hover:bg-gray-700",
      className
    )}
  >
    {icon}
  </button>
);

const HeaderIcons = ({ icons }: { icons: React.ReactNode[] }) => (
  <div className="flex space-x-3 text-gray-400">
    {icons.map((icon, index) => {
      const iconType = (icon as any)?.type?.name || `icon-${index}`;
      return <IconButton key={`header-${iconType}`} icon={icon} />;
    })}
  </div>
);

const EvaluationMessage = ({ content }: { content: string }) => {
  const [shared, setShared] = useState(false);
  const parts = content.split("Correctly identified items:");
  const scoreText = parts[0].trim();
  const scoreMatch = scoreText.match(/You scored (\d+) points/);
  const score = scoreMatch ? parseInt(scoreMatch[1], 10) : 0;

  const handleShare = () => {
    window.open(createTwitterShareUrl(score), "_blank");
    setShared(true);
  };

  return (
    <div className="pl-2">
      <div className="prose prose-invert prose-sm max-w-none">
        <ReactMarkdown>{content}</ReactMarkdown>
      </div>

      <div className="mt-4">
        {shared ? (
          <div className="text-green-500 flex items-center gap-2">
            <CheckCircle2 className="w-4 h-4" />
            Twitter share opened in a new tab!
          </div>
        ) : (
          <Button
            onClick={handleShare}
            className="flex items-center gap-2 bg-[#1d9bf0] hover:bg-[#1a8cd8] text-white"
          >
            <Share2 className="w-4 h-4" />
            Share on Twitter
          </Button>
        )}
      </div>
    </div>
  );
};

const TypingAnimation = () => {
  const [dots, setDots] = useState("");

  useEffect(() => {
    const interval = setInterval(() => {
      setDots((prev) => (prev.length >= 3 ? "" : prev + "."));
    }, 400);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="pl-2">
      <div className="prose prose-invert prose-sm max-w-none">
        <p className="flex items-center">
          Evaluating your answer
          <span className="inline-flex ml-1 min-w-[30px] font-bold text-xl leading-tight tracking-wider">
            {dots}
          </span>
        </p>
      </div>
    </div>
  );
};

const ChatMessage = ({ role, content }: Message) => {
  const isEvaluation =
    role === "assistant" &&
    content.includes("You scored") &&
    content.includes("Correctly identified items:");

  const isTyping =
    role === "assistant" && content === "Evaluating your answer...";

  return (
    <div className="mb-4">
      <div className="font-semibold mb-1">
        {role === "user" ? "You" : "ChatGPT"}:
      </div>

      {isEvaluation ? (
        <EvaluationMessage content={content} />
      ) : isTyping ? (
        <TypingAnimation />
      ) : (
        <div className="pl-2">
          <div className="prose prose-invert prose-sm max-w-none">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
        </div>
      )}
    </div>
  );
};

export default function ChatGPTFrame() {
  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<Message[]>([INITIAL_MESSAGE]);
  const contentRef = useRef<HTMLDivElement>(null);
  const { sendAnswer, answerResult, isSubmittingAnswer } = useGameAnswer();

  const isInputDisabled = isSubmittingAnswer;
  const isButtonDisabled = !inputValue.trim() || isInputDisabled;

  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [messages]);

  // Handle evaluation result
  useEffect(() => {
    if (answerResult) {
      try {
        // Ensure we have valid data
        const totalPoints =
          typeof answerResult.totalPoints === "number"
            ? answerResult.totalPoints
            : 0;

        const correctAnswers = Array.isArray(answerResult.correctAnswers)
          ? answerResult.correctAnswers
          : [];

        const responseContent = formatEvaluationContent(
          totalPoints,
          correctAnswers
        );

        setMessages((prev) => [
          ...prev.filter((msg) => msg.content !== "Evaluating your answer..."),
          { role: "assistant", content: responseContent },
        ]);
      } catch (error) {
        console.error(
          "Error formatting evaluation result:",
          error,
          answerResult
        );
        setMessages((prev) => [
          ...prev.filter((msg) => msg.content !== "Evaluating your answer..."),
          {
            role: "assistant",
            content:
              "There was an error evaluating your answer. Please try again.",
          },
        ]);
      }
    }
  }, [answerResult]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isButtonDisabled) return;

    const userMessage = inputValue.trim();

    setMessages((prev) => [
      ...prev,
      { role: "user", content: userMessage },
      { role: "assistant", content: "Evaluating your answer..." },
    ]);

    setInputValue("");
    await sendAnswer(userMessage);
  };

  return (
    <div className="flex flex-col overflow-hidden border border-gray-700 shadow-xl h-full bg-[#343541]">
      <div className="flex items-center px-3 py-2 bg-[#1e1e1e] border-b border-gray-700">
        <HeaderIcons
          icons={[
            <Layers size={18} key="layers" />,
            <Sparkles size={18} key="sp" />,
          ]}
        />

        <div className="flex items-center text-white font-medium ml-4">
          ChatGPT
          <ChevronRight size={20} className="ml-1" />
        </div>

        <div className="ml-auto">
          <HeaderIcons
            icons={[
              <Download size={18} key="download" />,
              <Layers size={18} key="layers" />,
            ]}
          />
        </div>
      </div>

      <div ref={contentRef} className="flex-1 overflow-y-auto p-4 text-white">
        {messages.map((message, index) => (
          <ChatMessage
            key={`message-${message.role}-${index}`}
            role={message.role}
            content={message.content}
          />
        ))}
      </div>

      <div className="p-3 bg-[#343541]">
        <form onSubmit={handleSubmit} className="relative">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            disabled={isInputDisabled}
            placeholder="Type your answer..."
            className={cn(
              "w-full py-3 px-4 pr-16 rounded-lg text-white placeholder-gray-400 outline-none",
              isInputDisabled
                ? "bg-[#2a2b36] cursor-not-allowed"
                : "bg-[#40414f]"
            )}
          />
          <div className="absolute bottom-0 right-0 flex items-center h-full px-3 space-x-2 text-gray-400">
            <button
              type="submit"
              className={cn(
                "p-1 rounded",
                isButtonDisabled
                  ? "bg-gray-700 text-gray-500 cursor-not-allowed"
                  : "bg-white/10 hover:bg-gray-600 text-white cursor-pointer"
              )}
              disabled={isButtonDisabled}
            >
              <Waveform size={18} />
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
