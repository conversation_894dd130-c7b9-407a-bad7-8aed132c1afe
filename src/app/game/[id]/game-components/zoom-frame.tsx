"use client";

import { useState, type ReactNode } from "react";
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Users,
  MessageSquare,
  Heart,
  Share2,
  Shield,
  Info,
  Grid,
  MoreHorizontal,
  X,
  Music,
} from "lucide-react";

interface ZoomFrameProps {
  children: ReactNode;
  username?: string;
  showControls?: boolean;
  darkMode?: boolean;
  onClose?: () => void;
}

export default function ZoomFrame({
  children,
  username = "",
  showControls = true,
  darkMode = true,
  onClose,
}: ZoomFrameProps) {
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [videoEnabled, setVideoEnabled] = useState(true);
  const time = "01:39";

  return (
    <div
      className={`flex flex-col w-full h-full rounded-lg overflow-hidden border border-gray-700 ${
        darkMode ? "bg-[#1a1a1a] text-white" : "bg-white text-black"
      }`}
    >
      <div className="flex items-center p-2 border-b border-gray-700">
        <div className="flex-1 flex justify-center">
          <div className="px-4 py-1 bg-[#2d2d2d] rounded-md flex items-center text-sm">
            <Music className="w-4 h-4 mr-2 text-[#f97316]" />
            <span>Set up professional audio in</span>
            <span className="ml-1 text-[#f0f0f0] font-medium">
              Audio settings
            </span>
            <button className="ml-4 text-gray-400 hover:text-white">
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
        <div className="flex items-center">
          <span className="text-sm font-medium">{time}</span>
          <button className="ml-2">
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M8 12L2 6H14L8 12Z" fill="currentColor" />
            </svg>
          </button>
        </div>
      </div>

      <div className="flex-1 flex items-center justify-center overflow-hidden">
        {children}
      </div>

      <div className="absolute bottom-16 left-4 text-sm font-medium">
        {username}
      </div>

      {showControls && (
        <div className="flex items-center justify-between p-2 border-t border-gray-700 bg-[#222]">
          <div className="flex items-center space-x-4">
            <button
              className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded"
              onClick={() => setAudioEnabled(!audioEnabled)}
            >
              {audioEnabled ? (
                <Mic className="w-5 h-5" />
              ) : (
                <MicOff className="w-5 h-5 text-red-500" />
              )}
              <span className="text-xs mt-1">Audio</span>
            </button>

            <button
              className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded"
              onClick={() => setVideoEnabled(!videoEnabled)}
            >
              {videoEnabled ? (
                <Video className="w-5 h-5" />
              ) : (
                <VideoOff className="w-5 h-5 text-red-500" />
              )}
              <span className="text-xs mt-1">Video</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Users className="w-5 h-5" />
              <div className="flex items-center text-xs mt-1">
                <span>Participants</span>
                <span className="ml-1">1</span>
              </div>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <MessageSquare className="w-5 h-5" />
              <span className="text-xs mt-1">Chat</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Heart className="w-5 h-5" />
              <span className="text-xs mt-1">React</span>
            </button>
          </div>

          <div className="flex items-center space-x-4">
            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Share2 className="w-5 h-5" />
              <span className="text-xs mt-1">Share</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Shield className="w-5 h-5" />
              <span className="text-xs mt-1">Host tools</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Info className="w-5 h-5" />
              <span className="text-xs mt-1">Meeting info</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <Grid className="w-5 h-5" />
              <span className="text-xs mt-1">Apps</span>
            </button>

            <button className="flex flex-col items-center justify-center p-2 hover:bg-gray-700 rounded">
              <MoreHorizontal className="w-5 h-5" />
              <span className="text-xs mt-1">More</span>
            </button>
          </div>

          {onClose && (
            <button
              className="flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 rounded"
              onClick={onClose}
            >
              <span>End</span>
            </button>
          )}
        </div>
      )}
    </div>
  );
}
