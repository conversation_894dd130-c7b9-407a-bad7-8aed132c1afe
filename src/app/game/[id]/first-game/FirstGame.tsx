"use client";

import { useState } from "react";
import ChatGPTFrame from "../game-components/chat-gpt-frame";
import { MacOS } from "../game-components/macos-screen";
import { FirstGameProvider } from "./first-game.context";
import JobDescriptionPage from "./job-description-page";
import MetamaskContent from "./MetamaskContent";
import { TwitterChatPage } from "./twitter-chat-page";
import ZoomMeetingContent from "./zoom-meeting-content";
import { ErrorHandler } from "./ErrorHandler";

export function FirstGame() {
  const [showJobDescription, setShowJobDescription] = useState(false);

  const gameInstructions = (
    <div className="space-y-4">
      <p>
        Welcome to ZeroTrust, an immersive security awareness experience
        inspired by real world hacking incidents. Your mission is to think like
        an investigator: spot red flags, follow clues, and outsmart the "hacker"
        by playing along step by step until the very end. Only by engaging fully
        with every instruction can you emerge victorious.
      </p>

      <h3 className="text-lg mt-4 font-bold underline">How to win?</h3>
      <ul className="list-disc pl-5 space-y-1">
        <li>
          Engage with every prompt from the "hacker" & let you go with his
          instructions
        </li>
        <li>Report all red flags</li>
      </ul>

      <h3 className="text-lg font-bold underline mt-4">How to play?</h3>
      <p className="mt-2">
        Interact with the hacker, it seems like he has reached to you on X… ➡️
        Open Chrome tab
      </p>

      <h3 className="text-lg font-bold underline mt-4">💯 Scoring system</h3>
      <p>Answers the question in your Chatgpt tab</p>
      <p>
        Detail matters: the more specific your observations, the higher your
        score
      </p>
    </div>
  );

  const chromeConfig = {
    extensions: [
      {
        id: "metamask",
        name: "MetaMask",
        icon: "/metamask.webp",
        content: <MetamaskContent />,
        active: true,
      },
      {
        id: "lastpass",
        name: "LastPass",
        icon: "https://lastpass.com/favicon.ico",
        content: (
          <div className="p-4 bg-white h-full flex flex-col items-center justify-center">
            <div className="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center mb-4">
              <span className="text-white text-2xl">LP</span>
            </div>
            <h2 className="text-xl font-bold mb-2">LastPass</h2>
            <p className="text-gray-600 text-center max-w-xs">
              Your passwords and personal information are securely stored in
              your vault.
            </p>
          </div>
        ),
        active: true,
      },
      {
        id: "grammarly",
        name: "Grammarly",
        icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4cHgiIGhlaWdodD0iNDhweCI+PHBhdGggZmlsbD0iIzE1QzM5QSIgZD0iTTI0LDRDMTIuOTU0LDQsNCwxMi45NTQsNCwyNHM4Ljk1NCwyMCwyMCwyMHMyMC04Ljk1NCwyMC0yMFMzNS4wNDYsNCwyNCw0eiIvPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0yNCwzMmMtMC40MjcsMC0wLjg1NS0wLjE2NC0xLjE4MS0wLjQ5MWMtMC4zMjctMC4zMjYtMC40OTEtMC43NTQtMC40OTEtMS4xODFWMTcuNjcyYzAtMC40MjcsMC4xNjQtMC44NTUsMC40OTEtMS4xODFjMC4zMjYtMC4zMjcsMC43NTQtMC40OTEsMS4xODEtMC40OTFjMC40MjcsMCwwLjg1NSwwLjE2NCwxLjE4MSwwLjQ5MWMwLjMyNywwLjMyNiwwLjQ5MSwwLjc1NCwwLjQ5MSwxLjE4MXYxMi42NTZjMCwwLjQyNy0wLjE2NCwwLjg1NS0wLjQ5MSwxLjE4MUMyNC44NTUsMzEuODM2LDI0LjQyNywzMiwyNCwzMnoiLz48cGF0aCBmaWxsPSIjZmZmIiBkPSJNMzAuMzI4LDI1LjY3MmMtMC40MjcsMC0wLjg1NS0wLjE2NC0xLjE4MS0wLjQ5MWMtMC4zMjctMC4zMjYtMC40OTEtMC43NTQtMC40OTEtMS4xODFjMC0yLjUxNC0yLjE0Mi00LjY1Ni00LjY1Ni00LjY1NmMtMi41MTQsMC00LjY1NiwyLjE0Mi00LjY1Niw0LjY1NmMwLDAuNDI3LTAuMTY0LDAuODU1LTAuNDkxLDEuMTgxYy0wLjMyNiwwLjMyNy0wLjc1NCwwLjQ5MS0xLjE4MSwwLjQ5MWMtMC40MjcsMC0wLjg1NS0wLjE2NC0xLjE4MS0wLjQ5MWMtMC4zMjctMC4zMjYtMC40OTEtMC43NTQtMC40OTEtMS4xODFjMC00LjIyMywzLjQzMy03LjY1Niw3LjY1Ni03LjY1NmM0LjIyMywwLDcuNjU2LDMuNDMzLDcuNjU2LDcuNjU2YzAsMC40MjctMC4xNjQsMC44NTUtMC40OTEsMS4xODFDMzEuMTgzLDI1LjUwOCwzMC43NTUsMjUuNjcyLDMwLjMyOCwyNS42NzJ6Ii8+PC9zdmc+",
        content: (
          <div className="p-4 bg-white h-full flex flex-col items-center justify-center">
            <div className="w-16 h-16 bg-[#15C39A] rounded-full flex items-center justify-center mb-4">
              <span className="text-white text-2xl">G</span>
            </div>
            <h2 className="text-xl font-bold mb-2">Grammarly</h2>
            <p className="text-gray-600 text-center max-w-xs">
              Grammarly helps you communicate confidently and write effectively.
            </p>
          </div>
        ),
        active: true,
      },
    ],
    tabs: [
      {
        name: "X messages",
        url: "https://twitter.com",
        closable: false,
        content: (
          <TwitterChatPage
            onJobDescriptionClick={() => setShowJobDescription(true)}
          />
        ),
      },
      ...(showJobDescription
        ? [
            {
              name: "Job Description",
              url: "https://nexusfi.com/careers/senior-defi-protocol-engineer",
              closable: true,
              content: <JobDescriptionPage />,
            },
          ]
        : []),
    ],
  };

  return (
    <FirstGameProvider>
      <ErrorHandler>
        <MacOS
          chromeConfig={chromeConfig}
          gameInstructions={gameInstructions}
          chatgpt={{ content: <ChatGPTFrame /> }}
          zoom={{
            content: <ZoomMeetingContent />,
          }}
        />
      </ErrorHandler>
    </FirstGameProvider>
  );
}
