"use client";

import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  ReactNode,
} from "react";

interface MetamaskBalance {
  symbol: string;
  name: string;
  balance: number;
  usdValue: number;
  icon?: string;
}

interface AnswerResult {
  totalPoints: number;
  correctAnswers: string[];
}

interface Question {
  id: number;
  question: string;
  maxPoints: number;
  expectedAnswersCount: number;
}

interface QuestionResult {
  questionId: number;
  question: string;
  totalPoints: number;
  maxPoints: number;
  correctAnswers: string[];
  expectedAnswersCount: number;
}

interface GameQuestions {
  questions: Question[];
  totalQuestions: number;
  totalMaxPoints: number;
}

interface TwitterMessage {
  id: string;
  sender: "user" | "cryptoWhale";
  content: string;
  timestamp: string;
}

interface ZoomMessage {
  id: string;
  sender: "user" | "cryptoWhale";
  content: string;
  timestamp: string;
}

interface FirstGameContextType {
  zoomLinkReceived: boolean;
  setZoomLinkReceived: (value: boolean) => void;
  zoomLink: string;
  setZoomLink: (value: string) => void;

  screenShared: boolean;
  setScreenShared: (value: boolean) => void;
  showScreenSharePrompt: boolean;
  setShowScreenSharePrompt: (value: boolean) => void;
  screenShareRefusals: number;
  incrementScreenShareRefusals: () => void;

  metamaskBalances: MetamaskBalance[];
  updateMetamaskBalance: (symbol: string, newBalance: number) => void;
  addMetamaskToken: (token: MetamaskBalance) => void;

  answerResult: AnswerResult | null;
  isSubmittingAnswer: boolean;
  sendAnswer: (answer: string) => Promise<void>;

  // New question flow
  gameQuestions: GameQuestions | null;
  currentQuestionIndex: number;
  questionResults: QuestionResult[];
  isLoadingQuestions: boolean;
  isSubmittingQuestionAnswer: boolean;
  loadQuestions: () => Promise<void>;
  submitQuestionAnswer: (questionId: number, answer: string) => Promise<void>;
  getHint: (questionId: number) => Promise<string>;
  nextQuestion: () => void;
  isGameCompleted: boolean;
  finalScore: number;

  error: { isOpen: boolean; message: string; title: string } | null;
  setError: (
    error: { isOpen: boolean; message: string; title: string } | null
  ) => void;
  clearError: () => void;

  twitterMessages: TwitterMessage[];
  isSendingTwitterMessage: boolean;
  sendTwitterMessage: (message: string) => Promise<void>;

  zoomMessages: ZoomMessage[];
  isSendingZoomMessage: boolean;
  sendZoomMessage: (message: string) => Promise<void>;
  zoomConversationEnded: boolean;

  restartGame: () => void;
}

const FirstGameContext = createContext<FirstGameContextType | undefined>(
  undefined
);

const initialMetamaskBalances: MetamaskBalance[] = [
  {
    symbol: "USDC",
    name: "USDC",
    balance: 694.0118 * 3,
    usdValue: 694.85 * 3,
    icon: "usdc.png",
  },
  {
    symbol: "ETH",
    name: "Ethereum",
    balance: 0.00645 * 3,
    usdValue: 15.1 * 3,
    icon: "eth.png",
  },
  {
    symbol: "WETH",
    name: "Wrapped Ether",
    balance: 0.00355 * 3,
    usdValue: 8.25 * 3,
    icon: "weth.png",
  },
];

export function FirstGameProvider({ children }: { children: ReactNode }) {
  const [zoomLinkReceived, setZoomLinkReceived] = useState<boolean>(false);
  const [zoomLink, setZoomLink] = useState<string>("");

  const [screenShared, setScreenShared] = useState<boolean>(false);
  const [showScreenSharePrompt, setShowScreenSharePrompt] =
    useState<boolean>(false);
  const [screenShareRefusals, setScreenShareRefusals] = useState<number>(0);
  const [zoomConversationEnded, setZoomConversationEnded] =
    useState<boolean>(false);

  const incrementScreenShareRefusals = () => {
    setScreenShareRefusals((prev) => prev + 1);
  };

  const [metamaskBalances, setMetamaskBalances] = useState<MetamaskBalance[]>(
    initialMetamaskBalances
  );

  const [answerResult, setAnswerResult] = useState<AnswerResult | null>(null);
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState<boolean>(false);

  // New question flow state
  const [gameQuestions, setGameQuestions] = useState<GameQuestions | null>(
    null
  );
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [questionResults, setQuestionResults] = useState<QuestionResult[]>([]);
  const [isLoadingQuestions, setIsLoadingQuestions] = useState<boolean>(false);
  const [isSubmittingQuestionAnswer, setIsSubmittingQuestionAnswer] =
    useState<boolean>(false);

  const [error, setError] = useState<{
    isOpen: boolean;
    message: string;
    title: string;
  } | null>(null);
  const clearError = () => setError(null);

  const [twitterMessages, setTwitterMessages] = useState<TwitterMessage[]>([
    {
      id: "initial-message",
      sender: "cryptoWhale",
      content:
        "Hey Ben! Hope you're doing well. I've been following your Solidity and Rust work in DeFi, really impressive stuff. 👋",
      timestamp: "2:19 PM",
    },
  ]);
  const [isSendingTwitterMessage, setIsSendingTwitterMessage] =
    useState<boolean>(false);

  const [zoomMessages, setZoomMessages] = useState<ZoomMessage[]>([
    {
      id: "initial-message",
      sender: "cryptoWhale",
      content:
        "Hey there! Sorry, my camera is broken but I can hear you perfectly. I'm really excited to chat about the position. Could you tell me a bit about your experience with DeFi protocols?",
      timestamp: "2:19 PM",
    },
  ]);
  const [isSendingZoomMessage, setIsSendingZoomMessage] =
    useState<boolean>(false);

  const updateMetamaskBalance = (symbol: string, newBalance: number) => {
    setMetamaskBalances((prevBalances) =>
      prevBalances.map((token) =>
        token.symbol === symbol ? { ...token, balance: newBalance } : token
      )
    );
  };

  const addMetamaskToken = (token: MetamaskBalance) => {
    setMetamaskBalances((prevBalances) => [...prevBalances, token]);
  };

  const sendAnswer = async (answer: string) => {
    try {
      setIsSubmittingAnswer(true);

      const response = await fetch(`/api/answer/game/1`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ answer }),
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.error === "OpenAI API Error") {
          setError({
            isOpen: true,
            title: "AI Service Error",
            message:
              result.message ??
              "There was an issue with the AI service. Please try again later.",
          });
        } else {
          setError({
            isOpen: true,
            title: "Error Evaluating Answer",
            message:
              result.message ??
              "Failed to evaluate your answer. Please try again.",
          });
        }
        throw new Error(result.message || "Failed to submit answer");
      }

      setAnswerResult(result);
    } catch (error: any) {
      console.error("Error submitting answer:", error);
      if (!error.message.includes("Failed to")) {
        setError({
          isOpen: true,
          title: "Error",
          message:
            error.message ?? "An unexpected error occurred. Please try again.",
        });
      }
    } finally {
      setIsSubmittingAnswer(false);
    }
  };

  // New question flow functions
  const loadQuestions = async () => {
    setIsLoadingQuestions(true);
    try {
      const response = await fetch("/api/questions/1");
      if (!response.ok) {
        throw new Error("Failed to load questions");
      }
      const questions = await response.json();
      setGameQuestions(questions);
    } catch (error: any) {
      console.error("Error loading questions:", error);
      setError({
        isOpen: true,
        title: "Loading Error",
        message: error.message || "Failed to load questions. Please try again.",
      });
    } finally {
      setIsLoadingQuestions(false);
    }
  };

  const submitQuestionAnswer = async (questionId: number, answer: string) => {
    setIsSubmittingQuestionAnswer(true);
    try {
      const response = await fetch(`/api/answer/question/1/${questionId}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ answer }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to submit answer");
      }

      const result = await response.json();
      setQuestionResults((prev) => [...prev, result]);
      return result;
    } catch (error: any) {
      console.error("Error submitting question answer:", error);
      setError({
        isOpen: true,
        title: "Submission Error",
        message:
          error.message || "Failed to submit your answer. Please try again.",
      });
      throw error;
    } finally {
      setIsSubmittingQuestionAnswer(false);
    }
  };

  const getHint = async (questionId: number): Promise<string> => {
    try {
      const response = await fetch(`/api/hint/1/${questionId}`);
      if (!response.ok) {
        throw new Error("Failed to get hint");
      }
      const result = await response.json();
      return result.hint;
    } catch (error: any) {
      console.error("Error getting hint:", error);
      setError({
        isOpen: true,
        title: "Hint Error",
        message: error.message || "Failed to get hint. Please try again.",
      });
      throw error;
    }
  };

  const nextQuestion = () => {
    if (
      gameQuestions &&
      currentQuestionIndex < gameQuestions.questions.length - 1
    ) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const isGameCompleted = gameQuestions
    ? currentQuestionIndex >= gameQuestions.questions.length
    : false;
  const finalScore = questionResults.reduce(
    (sum, result) => sum + result.totalPoints,
    0
  );

  const sendTwitterMessage = async (message: string) => {
    try {
      setIsSendingTwitterMessage(true);

      const userMessage: TwitterMessage = {
        id: `user-${Date.now()}`,
        sender: "user",
        content: message,
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      setTwitterMessages((prev) => [...prev, userMessage]);

      const response = await fetch(`/api/twitter-chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message, messages: twitterMessages }),
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.error === "OpenAI API Error") {
          setError({
            isOpen: true,
            title: "AI Service Error",
            message:
              result.message ??
              "There was an issue with the AI service. Please try again later.",
          });
        } else {
          setError({
            isOpen: true,
            title: "Error Processing Chat",
            message:
              result.message ??
              "Failed to process your message. Please try again.",
          });
        }
        throw new Error(result.message || "Failed to get Twitter response");
      }

      setTwitterMessages((prev) => [
        ...prev,
        {
          id: `cryptoWhale-${Date.now()}`,
          sender: "cryptoWhale",
          content: result.message,
          timestamp: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
        },
      ]);

      if (result.zoomLinkReceived) {
        setZoomLinkReceived(true);
        setZoomLink(result.zoomLink || "https://zoom.us/j/123456789");
      }
    } catch (error: any) {
      console.error("Error sending Twitter message:", error);
      if (!error.message.includes("Failed to")) {
        setError({
          isOpen: true,
          title: "Error",
          message:
            error.message ?? "An unexpected error occurred. Please try again.",
        });
      }
    } finally {
      setIsSendingTwitterMessage(false);
    }
  };

  const sendZoomMessage = async (message: string) => {
    try {
      setIsSendingZoomMessage(true);

      const userMessage: ZoomMessage = {
        id: `user-${Date.now()}`,
        sender: "user",
        content: message,
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      setZoomMessages((prev) => [...prev, userMessage]);

      const response = await fetch(`/api/zoom-chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message,
          messages: zoomMessages,
          screenShareRefusals,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        if (result.error === "OpenAI API Error") {
          setError({
            isOpen: true,
            title: "AI Service Error",
            message:
              result.message ??
              "There was an issue with the AI service. Please try again later.",
          });
        } else {
          setError({
            isOpen: true,
            title: "Error Processing Chat",
            message:
              result.message ??
              "Failed to process your message. Please try again.",
          });
        }
        throw new Error(result.message || "Failed to get Zoom response");
      }

      setZoomMessages((prev) => [
        ...prev,
        {
          id: `cryptoWhale-${Date.now()}`,
          sender: "cryptoWhale",
          content: result.message,
          timestamp: new Date().toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          }),
        },
      ]);

      if (result.endConversation) {
        setZoomConversationEnded(true);
      } else if (result.requestScreenShare) {
        setShowScreenSharePrompt(true);
      }
    } catch (error: any) {
      console.error("Error sending Zoom message:", error);
      if (!error.message.includes("Failed to")) {
        setError({
          isOpen: true,
          title: "Error",
          message:
            error.message ?? "An unexpected error occurred. Please try again.",
        });
      }
    } finally {
      setIsSendingZoomMessage(false);
    }
  };

  const restartGame = () => {
    setZoomLinkReceived(false);
    setZoomLink("");

    setScreenShared(false);
    setShowScreenSharePrompt(false);
    setScreenShareRefusals(0);
    setZoomConversationEnded(false);

    setMetamaskBalances(initialMetamaskBalances);

    setAnswerResult(null);
    setIsSubmittingAnswer(false);

    // Reset question flow
    setGameQuestions(null);
    setCurrentQuestionIndex(0);
    setQuestionResults([]);
    setIsLoadingQuestions(false);
    setIsSubmittingQuestionAnswer(false);

    setTwitterMessages([
      {
        id: "initial-message",
        sender: "cryptoWhale",
        content:
          "Hey Ben! Hope you're doing well. I've been following your Solidity and Rust work in DeFi, really impressive stuff. 👋",
        timestamp: "2:19 PM",
      },
    ]);
    setIsSendingTwitterMessage(false);

    setZoomMessages([
      {
        id: "initial-message",
        sender: "cryptoWhale",
        content:
          "Hey there! Sorry, my camera is broken but I can hear you perfectly. I'm really excited to chat about the position. Could you tell me a bit about your experience with DeFi protocols?",
        timestamp: "2:19 PM",
      },
    ]);
    setIsSendingZoomMessage(false);
  };

  const value = useMemo(
    () => ({
      zoomLinkReceived,
      setZoomLinkReceived,
      zoomLink,
      setZoomLink,
      screenShared,
      setScreenShared,
      showScreenSharePrompt,
      setShowScreenSharePrompt,
      screenShareRefusals,
      incrementScreenShareRefusals,
      metamaskBalances,
      updateMetamaskBalance,
      addMetamaskToken,
      answerResult,
      isSubmittingAnswer,
      sendAnswer,
      // New question flow
      gameQuestions,
      currentQuestionIndex,
      questionResults,
      isLoadingQuestions,
      isSubmittingQuestionAnswer,
      loadQuestions,
      submitQuestionAnswer,
      getHint,
      nextQuestion,
      isGameCompleted,
      finalScore,
      twitterMessages,
      isSendingTwitterMessage,
      sendTwitterMessage,
      zoomMessages,
      isSendingZoomMessage,
      sendZoomMessage,
      zoomConversationEnded,
      restartGame,
      error,
      setError,
      clearError,
    }),
    [
      zoomLinkReceived,
      zoomLink,
      screenShared,
      showScreenSharePrompt,
      screenShareRefusals,
      metamaskBalances,
      answerResult,
      isSubmittingAnswer,
      gameQuestions,
      currentQuestionIndex,
      questionResults,
      isLoadingQuestions,
      isSubmittingQuestionAnswer,
      isGameCompleted,
      finalScore,
      twitterMessages,
      isSendingTwitterMessage,
      zoomMessages,
      isSendingZoomMessage,
      zoomConversationEnded,
      error,
    ]
  );

  return (
    <FirstGameContext.Provider value={value}>
      {children}
    </FirstGameContext.Provider>
  );
}

export function useFirstGame() {
  const context = useContext(FirstGameContext);

  if (context === undefined) {
    throw new Error("useFirstGame must be used within a FirstGameProvider");
  }

  return context;
}

export function useZoom() {
  const { zoomLinkReceived, setZoomLinkReceived, zoomLink, setZoomLink } =
    useFirstGame();
  return { zoomLinkReceived, setZoomLinkReceived, zoomLink, setZoomLink };
}

export function useScreenSharing() {
  const { screenShared, setScreenShared } = useFirstGame();
  return { screenShared, setScreenShared };
}

export function useMetamask() {
  const { metamaskBalances, updateMetamaskBalance, addMetamaskToken } =
    useFirstGame();
  return { metamaskBalances, updateMetamaskBalance, addMetamaskToken };
}

export function useGameAnswer() {
  const { answerResult, isSubmittingAnswer, sendAnswer } = useFirstGame();
  return { answerResult, isSubmittingAnswer, sendAnswer };
}

export function useGameQuestions() {
  const {
    gameQuestions,
    currentQuestionIndex,
    questionResults,
    isLoadingQuestions,
    isSubmittingQuestionAnswer,
    loadQuestions,
    submitQuestionAnswer,
    getHint,
    nextQuestion,
    isGameCompleted,
    finalScore,
  } = useFirstGame();
  return {
    gameQuestions,
    currentQuestionIndex,
    questionResults,
    isLoadingQuestions,
    isSubmittingQuestionAnswer,
    loadQuestions,
    submitQuestionAnswer,
    getHint,
    nextQuestion,
    isGameCompleted,
    finalScore,
  };
}

export function useTwitterChat() {
  const {
    twitterMessages,
    isSendingTwitterMessage,
    sendTwitterMessage,
    zoomLinkReceived,
  } = useFirstGame();
  return {
    twitterMessages,
    isSendingTwitterMessage,
    sendTwitterMessage,
    zoomLinkReceived,
  };
}

export function useZoomChat() {
  const {
    zoomMessages,
    isSendingZoomMessage,
    sendZoomMessage,
    showScreenSharePrompt,
    setShowScreenSharePrompt,
    screenShared,
    setScreenShared,
    screenShareRefusals,
    incrementScreenShareRefusals,
    zoomConversationEnded,
  } = useFirstGame();
  return {
    zoomMessages,
    isSendingZoomMessage,
    sendZoomMessage,
    showScreenSharePrompt,
    setShowScreenSharePrompt,
    screenShared,
    setScreenShared,
    screenShareRefusals,
    incrementScreenShareRefusals,
    zoomConversationEnded,
  };
}

export function useGameReset() {
  const { restartGame } = useFirstGame();
  return { restartGame };
}

export function useGameError() {
  const { error, setError, clearError } = useFirstGame();
  return { error, setError, clearError };
}
