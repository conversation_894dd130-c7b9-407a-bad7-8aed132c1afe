"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ZoomShareConfirmProps {
  username: string;
  onApprove: () => void;
  onDecline: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ZoomShareConfirm({
  username = "Zoom",
  onApprove = () => {},
  onDecline = () => {},
  open,
  onOpenChange,
}: ZoomShareConfirmProps) {
  const [confirmed, setConfirmed] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(open || false);

  const handleOpenChange = (newOpen: boolean) => {
    setDialogOpen(newOpen);
    onOpenChange?.(newOpen);
  };

  const handleApprove = () => {
    onApprove();
    handleOpenChange(false);
  };

  const handleDecline = () => {
    onDecline();
    handleOpenChange(false);
  };

  return (
    <Dialog
      open={open !== undefined ? open : dialogOpen}
      onOpenChange={handleOpenChange}
    >
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="gap-2">
          <DialogTitle className="text-2xl font-bold text-red-500 text-left">
            A suspicious user named &quot;{username}&quot; is requesting remote
            control of your computer
          </DialogTitle>
        </DialogHeader>
        <DialogDescription className="text-left space-y-4 pt-2">
          <p className="text-base text-foreground">
            If you approve, this person will be able to:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li className="text-base text-foreground">
              View and interact with your screen
            </li>
            <li className="text-base text-foreground">
              Install software or change settings
            </li>
          </ul>
          <p className="text-base font-semibold text-foreground">
            You can regain control at any time by clicking on your screen.
          </p>
          <div className="flex items-center space-x-2 pt-2">
            <Checkbox
              id="confirm"
              checked={confirmed}
              onCheckedChange={(checked) => setConfirmed(!!checked)}
            />
            <label
              htmlFor="confirm"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I confirm that I know and trust this user
            </label>
          </div>
        </DialogDescription>
        <DialogFooter className="sm:justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            className="w-24 text-base"
            onClick={handleDecline}
          >
            Decline
          </Button>
          <Button
            type="button"
            className="w-24 text-base"
            onClick={handleApprove}
            disabled={!confirmed}
          >
            Approve
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
