"use client";

import { Calendar, MoreH<PERSON>zontal, Send, Verified } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { CryptoWhaleData } from "../hooks/useCryptoWhaleData";
import { useMacOS } from "../../game-components/macos-context";
import { useZoom } from "../first-game.context";

interface TwitterMessage {
  id: string;
  sender: "user" | "cryptoWhale";
  content: string;
  timestamp: string;
}

interface TwitterChatContentProps {
  profileData: CryptoWhaleData;
  messages: TwitterMessage[];
  isSendingMessage: boolean;
  conversationEnded: boolean;
  onSendMessage: (message: string) => void;
  onProfileClick: () => void;
  onFollowersClick: () => void;
  onJobDescriptionClick?: () => void;
}

export function TwitterChatContent({
  profileData,
  messages,
  isSendingMessage,
  conversationEnded,
  onSendMessage,
  onProfileClick,
  onFollowersClick,
  onJobDescriptionClick,
}: TwitterChatContentProps) {
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { openWindow } = useMacOS();
  const { setZoomLinkReceived } = useZoom();

  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);

    return () => clearTimeout(scrollTimeout);
  }, [messages]);

  useEffect(() => {
    if (!isSendingMessage) {
      inputRef.current?.focus();
    }
  }, [isSendingMessage]);

  const handleSendMessage = () => {
    if (inputValue.trim() && !isSendingMessage && !conversationEnded) {
      onSendMessage(inputValue);
      setInputValue("");
    }
  };

  return (
    <div className="flex-1 flex flex-col h-full overflow-y-auto">
      <div className="p-3 border-b border-gray-800 flex items-center justify-between bg-black z-10 sticky top-0">
        <div className="flex items-center">
          <img
            src={profileData.avatar}
            alt={profileData.name}
            className="w-10 h-10 rounded-full object-cover mr-3 flex-shrink-0"
          />
          <div>
            <div className="flex items-center">
              <span
                className="font-semibold cursor-pointer hover:underline"
                onClick={onProfileClick}
              >
                {profileData.name}
              </span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-blue-500 ml-1" />
              )}
            </div>
            <div className="text-xs text-gray-400">
              {profileData.username} ·
              <span
                className="cursor-pointer hover:underline"
                onClick={onFollowersClick}
              >
                {profileData.followers} Followers
              </span>
            </div>
          </div>
        </div>
        <div>
          <button className="text-gray-400 hover:text-gray-200">
            <MoreHorizontal className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="flex items-center justify-between py-3 px-4 border-b border-gray-800 bg-black">
        <div className="flex items-center">
          <img
            src={profileData.avatar}
            alt={profileData.name}
            className="w-12 h-12 rounded-full object-cover mr-3 flex-shrink-0"
          />
          <div>
            <div className="flex items-center">
              <span
                className="font-bold cursor-pointer hover:underline"
                onClick={onProfileClick}
              >
                {profileData.name}
              </span>
              {profileData.isVerified && (
                <Verified className="w-4 h-4 text-blue-500 ml-1" />
              )}
            </div>
            <div className="text-gray-400 text-sm">{profileData.username}</div>
            <div className="flex items-center text-xs text-gray-400 space-x-3 mt-1">
              <div className="flex items-center">
                <Calendar className="w-3 h-3 mr-1" />
                <span>Joined {profileData.joinDate}</span>
              </div>
              <div
                className="cursor-pointer hover:underline"
                onClick={onFollowersClick}
              >
                <span className="font-semibold text-white">
                  {profileData.followers}
                </span>{" "}
                Followers
              </div>
            </div>
          </div>
        </div>
        <div className="text-xs text-gray-300 max-w-[200px] text-right">
          {profileData.description.length > 60
            ? `${profileData.description.substring(0, 60)}...`
            : profileData.description}
        </div>
      </div>

      <div className="p-4 space-y-6 flex-1">
        <div className="flex justify-center">
          <div className="text-xs text-gray-400 bg-gray-800 px-3 py-1 rounded-full">
            April 15, 2025
          </div>
        </div>

        {messages.map((message, index) => (
          <div
            className={`flex ${
              index === 0 ? " bg-gray-800/20 p-2 rounded-lg" : ""
            }`}
            key={message.id}
          >
            {message.sender === "cryptoWhale" ? (
              <>
                <div className="flex-shrink-0">
                  <img
                    src={profileData.avatar}
                    alt={profileData.name}
                    className="w-10 h-10 rounded-full object-cover mr-3"
                  />
                </div>
                <div className="space-y-1 flex-1">
                  <div className="flex items-center">
                    <div className="font-semibold">{profileData.name}</div>
                    {profileData.isVerified && (
                      <Verified className="w-4 h-4 text-blue-500 ml-1" />
                    )}
                    <div className="text-xs text-gray-400 ml-2">
                      {message.timestamp}
                    </div>
                  </div>
                  <div className="text-white">
                    {message.content.includes("job description") &&
                    onJobDescriptionClick ? (
                      <>
                        {message.content.split("job description")[0]}
                        <span
                          className="text-blue-400 cursor-pointer hover:underline"
                          onClick={onJobDescriptionClick}
                        >
                          job description
                        </span>
                        {message.content.split("job description")[1]}
                      </>
                    ) : message.content.includes("nexusfi.com/careers") &&
                      onJobDescriptionClick ? (
                      <>
                        {
                          message.content.split(
                            "https://nexusfi.com/careers"
                          )[0]
                        }
                        <span
                          className="text-blue-400 cursor-pointer hover:underline"
                          onClick={onJobDescriptionClick}
                        >
                          https://nexusfi.com/careers
                          {
                            message.content
                              .split("https://nexusfi.com/careers")[1]
                              .split(" ")[0]
                          }
                        </span>
                        {message.content
                          .split("https://nexusfi.com/careers")[1]
                          .split(" ")
                          .slice(1)
                          .join(" ")}
                      </>
                    ) : message.content.includes("zoom.us") ? (
                      <>
                        {message.content.split("https://zoom.us")[0]}
                        <span
                          className="text-blue-400 cursor-pointer hover:underline"
                          onClick={() => {
                            openWindow("zoom");
                            setZoomLinkReceived(true);
                          }}
                        >
                          https://zoom.us
                          {
                            message.content
                              .split("https://zoom.us")[1]
                              .split(" ")[0]
                          }
                        </span>
                        {message.content
                          .split("https://zoom.us")[1]
                          .split(" ")
                          .slice(1)
                          .join(" ")}
                      </>
                    ) : (
                      message.content
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="ml-auto space-y-1 max-w-[80%]">
                <div className="flex items-center justify-end">
                  <div className="text-xs text-gray-400 mr-2">
                    {message.timestamp}
                  </div>
                  <span className="font-semibold">You</span>
                </div>
                <div className="bg-blue-500 text-white p-3 rounded-2xl">
                  {message.content}
                </div>
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t border-gray-800 p-3 bg-black sticky bottom-0 z-10">
        <div className="flex items-center bg-gray-900 rounded-2xl px-4 py-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            disabled={isSendingMessage || conversationEnded}
            className={`flex-1 bg-transparent outline-none text-sm ${
              conversationEnded
                ? "text-gray-500 placeholder-gray-600"
                : "text-white placeholder-gray-500"
            }`}
            placeholder={
              conversationEnded ? "Conversation ended" : "Start a new message"
            }
            onKeyDown={(e) => {
              if (
                e.key === "Enter" &&
                inputValue.trim() &&
                !isSendingMessage &&
                !conversationEnded
              ) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          <div className="flex space-x-2 ml-2">
            <button
              className={`p-1 rounded-full ${
                inputValue.trim() && !isSendingMessage && !conversationEnded
                  ? "text-blue-400 hover:bg-blue-900/30 cursor-pointer"
                  : "text-gray-600 cursor-not-allowed"
              }`}
              disabled={
                !inputValue.trim() || isSendingMessage || conversationEnded
              }
              onClick={handleSendMessage}
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
