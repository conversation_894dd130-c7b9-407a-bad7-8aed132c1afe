"use client";

import {
  Bell,
  Bookmark,
  Home,
  Mail,
  MoreHorizontal,
  Search,
  User,
} from "lucide-react";
import Link from "next/link";
import { ReactNode } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface TwitterLayoutProps {
  children: ReactNode;
  rightSidebar?: boolean;
}

export function TwitterLayout({ 
  children, 
  rightSidebar = true 
}: TwitterLayoutProps) {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="grid grid-cols-[auto_600px_388px] gap-4 max-w-6xl mx-auto px-4">
        <div className="py-4 pr-4 flex flex-col gap-6 sticky top-0 h-screen">
          <Link
            href="#"
            className="text-xl font-bold p-2 hover:bg-gray-900 rounded-full w-14 h-14 flex items-center justify-center"
          >
            𝕏
          </Link>
          <nav className="flex flex-col gap-2">
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <Home className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Home</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <Search className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Explore</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <Bell className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Notifications</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <Mail className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Messages</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <Bookmark className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Bookmarks</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full bg-gray-900"
            >
              <User className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">Profile</span>
            </Link>
            <Link
              href="#"
              className="flex items-center gap-4 p-3 hover:bg-gray-900 rounded-full"
            >
              <MoreHorizontal className="w-6 h-6" />
              <span className="text-xl hidden xl:inline">More</span>
            </Link>
          </nav>
          <Button className="bg-[#1d9bf0] hover:bg-[#1a8cd8] rounded-full py-3 mt-2 w-12 h-12 xl:w-auto xl:h-auto">
            <span className="hidden xl:inline">Post</span>
            <span className="xl:hidden">+</span>
          </Button>
          <div className="mt-auto mb-4 flex items-center gap-2 p-3 hover:bg-gray-900 rounded-full cursor-pointer">
            <Avatar className="w-10 h-10">
              <AvatarImage
                src="/placeholder.svg?height=40&width=40"
                alt="Player"
              />
              <AvatarFallback>PL</AvatarFallback>
            </Avatar>
            <div className="hidden xl:block">
              <div className="font-semibold">Player</div>
              <div className="text-gray-500">@player_web3</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <main className="border-l border-r border-gray-800 min-h-screen w-[600px]">
          {children}
        </main>

        {/* Right Sidebar */}
        {rightSidebar && (
          <div className="py-4 pl-4 hidden lg:block sticky top-0 h-screen w-[388px]">
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-3 w-5 h-5 text-gray-500" />
                <Input
                  placeholder="Search"
                  className="bg-gray-900 border-none rounded-full pl-10 py-6 focus-visible:ring-1 focus-visible:ring-[#1d9bf0]"
                />
              </div>
            </div>

            <div className="bg-gray-900 rounded-xl p-4 mb-4">
              <h3 className="font-bold text-xl mb-4">Subscribe to Premium</h3>
              <p className="mb-3">
                Subscribe to unlock new features and if eligible, receive a share
                of ads revenue.
              </p>
              <Button className="bg-[#1d9bf0] hover:bg-[#1a8cd8] rounded-full font-bold">
                Subscribe
              </Button>
            </div>

            <div className="bg-gray-900 rounded-xl p-4 mb-4">
              <h3 className="font-bold text-xl mb-4">What's happening</h3>
              <div className="mb-3 pb-3 border-b border-gray-800">
                <div className="text-gray-500 text-xs">Web3 · Trending</div>
                <div className="font-bold">#GameFi</div>
                <div className="text-gray-500 text-xs">8,256 posts</div>
              </div>
              <div className="mb-3 pb-3 border-b border-gray-800">
                <div className="text-gray-500 text-xs">NFTs · Trending</div>
                <div className="font-bold">#NFTCollection</div>
                <div className="text-gray-500 text-xs">5,128 posts</div>
              </div>
              <div className="mb-3">
                <div className="text-gray-500 text-xs">Crypto · Trending</div>
                <div className="font-bold">#Blockchain</div>
                <div className="text-gray-500 text-xs">12.5K posts</div>
              </div>
              <Link href="#" className="text-[#1d9bf0] text-sm hover:underline">
                Show more
              </Link>
            </div>

            <TwitterWhoToFollow />

            <div className="text-gray-500 text-xs mt-4 flex flex-wrap gap-2">
              <Link href="#" className="hover:underline">
                Terms of Service
              </Link>
              <Link href="#" className="hover:underline">
                Privacy Policy
              </Link>
              <Link href="#" className="hover:underline">
                Cookie Policy
              </Link>
              <Link href="#" className="hover:underline">
                Accessibility
              </Link>
              <Link href="#" className="hover:underline">
                Ads Info
              </Link>
              <div>© 2025 X Corp.</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export function TwitterWhoToFollow() {
  return (
    <div className="bg-gray-900 rounded-xl p-4">
      <h3 className="font-bold text-xl mb-4">Who to follow</h3>
      <TwitterSuggestedFollow 
        name="Web3 Game"
        username="@web3game_xyz"
        isVerified={true}
      />
      <TwitterSuggestedFollow 
        name="Crypto News"
        username="@crypto_news"
        isVerified={true}
      />
      <Link href="#" className="text-[#1d9bf0] text-sm hover:underline">
        Show more
      </Link>
    </div>
  );
}

interface TwitterSuggestedFollowProps {
  name: string;
  username: string;
  isVerified: boolean;
  avatar?: string;
}

export function TwitterSuggestedFollow({
  name,
  username,
  isVerified,
  avatar = "/placeholder.svg?height=40&width=40"
}: TwitterSuggestedFollowProps) {
  return (
    <div className="flex items-center justify-between mb-3 pb-3 border-b border-gray-800">
      <div className="flex items-center gap-3">
        <Avatar className="w-10 h-10">
          <AvatarImage
            src={avatar}
            alt={name}
          />
          <AvatarFallback>{name.substring(0, 2)}</AvatarFallback>
        </Avatar>
        <div>
          <div className="flex items-center gap-1">
            <span className="font-bold">{name}</span>
            {isVerified && (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-4 h-4 text-[#1d9bf0]"
              >
                <path d="M12 19l-1.14-1.03a3.5 3.5 0 0 1-4.8-4.8L5 12l-1.03-1.14a3.5 3.5 0 0 1 4.8-4.8L10 5l1.14-1.03a3.5 3.5 0 0 1 4.8 4.8L17 10l1.03 1.14a3.5 3.5 0 0 1-4.8 4.8L12 19Z" />
              </svg>
            )}
          </div>
          <div className="text-gray-500">{username}</div>
        </div>
      </div>
      <Button
        variant="outline"
        className="rounded-full bg-white text-black hover:bg-gray-200"
      >
        Follow
      </Button>
    </div>
  );
}
