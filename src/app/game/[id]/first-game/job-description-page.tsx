import { Share2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

export default function JobDescriptionPage() {
  return (
    <div className="flex flex-col h-full overflow-auto">
      <main className="flex-1">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <h1 className="mb-6 text-center text-3xl font-semibold text-emerald-700">
            TechBlocks
          </h1>

          <h2 className="mb-2 text-center text-2xl font-medium text-gray-800">
            Senior DeFi Protocol Engineer / Engineering Lead (Solidity + Rust)
          </h2>

          <div className="mb-4 text-center">
            <span className="rounded-full bg-emerald-100 px-4 py-1 text-lg font-bold text-emerald-700">
              $250,000 - $350,000 Annual Salary
            </span>
          </div>

          <div className="mb-8 text-center text-gray-600">
            <span className="mr-2">Remote</span>
            <span className="text-gray-400">·</span>
            <span className="ml-2">Full time</span>
          </div>

          <div className="border-b">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="mx-auto mb-0 grid w-full max-w-[400px] grid-cols-2">
                <TabsTrigger
                  value="overview"
                  className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-600"
                >
                  OVERVIEW
                </TabsTrigger>
                <TabsTrigger
                  value="application"
                  className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-emerald-600"
                >
                  APPLICATION
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="pt-6">
                <div className="flex justify-between">
                  <h3 className="mb-4 text-xl font-medium">Description</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 text-emerald-700"
                  >
                    <Share2 className="h-4 w-4" />
                    <span>Share this job</span>
                  </Button>
                </div>

                <h4 className="mb-2 font-medium">About Us</h4>
                <p className="mb-4 text-gray-700">
                  TechBlocks is a leading decentralized finance protocol with
                  over $2 billion in Total Value Locked (TVL). Our platform
                  enables users to lend, borrow, and earn yield on their digital
                  assets across multiple blockchains. We're backed by top-tier
                  VCs including Paradigm, a16z, and Sequoia.
                </p>
                <p className="mb-6 text-gray-700">
                  We're expanding our cross-chain capabilities and building the
                  next generation of DeFi infrastructure that will redefine
                  capital efficiency in the blockchain ecosystem.
                </p>

                <h4 className="mb-2 font-medium">The Role</h4>
                <p className="mb-4 text-gray-700">
                  We're seeking an exceptional DeFi protocol engineer to lead
                  the development of our next-generation lending and liquidity
                  protocols. You'll be responsible for designing and
                  implementing smart contracts that handle billions in user
                  funds while maintaining the highest security standards.
                </p>
                <p className="mb-4 text-gray-700">
                  You'll need deep expertise in Solidity and Rust to build
                  cross-chain protocols that operate seamlessly across
                  EVM-compatible chains and emerging L1/L2 ecosystems like
                  Solana, Arbitrum, and Optimism.
                </p>
                <p className="mb-6 text-gray-700">
                  This is a high-impact role where you'll shape the technical
                  direction of our protocol and work directly with our founders
                  and core team to build the future of decentralized finance.
                </p>

                <h4 className="mb-2 font-medium">Responsibilities</h4>
                <p className="mb-4 text-gray-700">
                  Design, implement, and audit smart contracts for our lending
                  and liquidity protocols.
                </p>
                <ul className="mb-6 space-y-3">
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Lead a team of smart contract engineers and establish best
                      practices for protocol development.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Architect cross-chain solutions that enable seamless asset
                      transfers and liquidity provision across multiple
                      blockchains.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Implement innovative DeFi mechanisms for yield generation,
                      risk management, and capital efficiency.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Collaborate with security researchers and external
                      auditors to ensure the highest standards of protocol
                      safety.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Work with economists and researchers to design and
                      implement novel tokenomics and incentive structures.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Write comprehensive documentation and contribute to
                      educational content about our protocol.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Participate in protocol governance discussions and help
                      shape the future direction of the ecosystem.
                    </span>
                  </li>
                </ul>

                <h4 className="mb-2 font-medium">Requirements</h4>
                <p className="mb-4 text-gray-700">
                  5+ years of software engineering experience with at least 3
                  years focused on blockchain development.
                </p>
                <ul className="mb-4 space-y-3">
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Deep expertise in Solidity and EVM internals with a proven
                      track record of secure smart contract development.
                    </span>
                  </li>
                </ul>
                <p className="mb-4 text-gray-700">
                  (Experience with major DeFi protocols like Aave, Compound,
                  Uniswap, or MakerDAO is highly valued.)
                </p>
                <ul className="mb-6 space-y-3">
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Strong understanding of DeFi primitives including AMMs,
                      lending protocols, derivatives, and yield strategies.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Experience with Rust and/or development on non-EVM chains
                      like Solana, Cosmos, or Polkadot.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Familiarity with blockchain security best practices and
                      common attack vectors in DeFi.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Experience with testing frameworks like Hardhat, Foundry,
                      or Brownie and formal verification tools.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Ability to work in a fast-paced, remote-first environment
                      with a global team.
                    </span>
                  </li>
                </ul>

                <h4 className="mb-2 font-medium">Bonus Points</h4>
                <ul className="mb-8 space-y-3">
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Contributions to major DeFi protocols or blockchain
                      infrastructure projects.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Experience with MEV protection, gas optimization, and EVM
                      internals.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Background in quantitative finance, market making, or
                      traditional financial systems.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Active participation in DeFi governance or protocol design
                      discussions.
                    </span>
                  </li>
                </ul>

                <h4 className="mb-2 font-medium">Benefits</h4>
                <ul className="mb-8 space-y-3">
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Competitive salary ($250K-$350K) plus token incentives and
                      equity.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Flexible work schedule and fully remote position with
                      quarterly team offsites.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>
                      Comprehensive health, dental, and vision insurance.
                    </span>
                  </li>
                  <li className="flex items-start gap-2 text-gray-700">
                    <span className="mt-1 flex h-4 w-4 shrink-0 rounded-full bg-gray-800"></span>
                    <span>Home office stipend and latest equipment.</span>
                  </li>
                </ul>

                <Button className="w-full bg-emerald-700 py-6 hover:bg-emerald-800">
                  Apply for this job
                </Button>
              </TabsContent>

              <TabsContent value="application">
                <div className="py-8 text-center text-gray-500">
                  Application form would go here
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  );
}
