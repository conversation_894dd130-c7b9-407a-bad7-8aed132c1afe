"use client";

import { useState } from "react";
import { FaXTwitter } from "react-icons/fa6";
import { FcGoogle } from "react-icons/fc";
import { FaApple } from "react-icons/fa";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function TwitterSignInScreen() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  return (
    <div className="flex flex-col min-h-full bg-black text-white">
      <div className="flex justify-center p-4">
        <FaXTwitter className="w-10 h-10 text-white" />
      </div>

      <div className="flex-1 flex flex-col items-center justify-center p-6 max-w-md mx-auto w-full">
        <div className="w-full">
          <h1 className="text-3xl font-bold mb-8 text-center">Sign in to X</h1>

          <div className="space-y-3 mb-6">
            <button className="w-full flex items-center justify-center gap-3 py-6 border border-gray-700 hover:bg-gray-900/50 rounded-full">
              <FcGoogle className="w-5 h-5" />
              <span className="font-medium">Continue with Google</span>
            </button>

            <button className="w-full flex items-center justify-center gap-3 py-6 border border-gray-700 hover:bg-gray-900/50 rounded-full">
              <FaApple className="w-5 h-5" />
              <span className="font-medium">Continue with Apple</span>
            </button>
          </div>

          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-800"></div>
            </div>
            <div className="relative flex justify-center">
              <span className="bg-black px-4 text-sm text-gray-500">or</span>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <Input
                type="text"
                placeholder="Phone, email, or username"
                className="bg-black border border-gray-700 py-6 text-white rounded-md focus:border-[#1d9bf0] focus:ring-[#1d9bf0]/30"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
              />
            </div>

            <div>
              <Input
                type="password"
                placeholder="Password"
                className="bg-black border border-gray-700 py-6 text-white rounded-md focus:border-[#1d9bf0] focus:ring-[#1d9bf0]/30"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <Button
              type="submit"
              className="w-full py-6 bg-white text-black hover:bg-gray-200 rounded-full font-bold text-base"
              disabled={isLoading}
            >
              {isLoading ? "Signing in..." : "Sign in"}
            </Button>
          </form>

          <div className="flex justify-between mt-4">
            <Button
              variant="link"
              className="text-gray-400 hover:text-white p-0"
            >
              Forgot password?
            </Button>

            <Button
              variant="link"
              className="text-gray-400 hover:text-white p-0"
            >
              Sign up for X
            </Button>
          </div>

          <div className="mt-12 text-xs text-gray-500">
            <p className="mb-2">
              By signing in, you agree to our{" "}
              <button className="text-[#1d9bf0] hover:underline p-0 inline font-normal">
                Terms
              </button>
              ,
              <button className="text-[#1d9bf0] hover:underline p-0 inline font-normal">
                {" "}
                Privacy Policy
              </button>
              , and
              <button className="text-[#1d9bf0] hover:underline p-0 inline font-normal">
                {" "}
                Cookie Use
              </button>
              .
            </p>
            <p>
              This site is protected by reCAPTCHA and the Google
              <button className="text-[#1d9bf0] hover:underline p-0 inline font-normal">
                {" "}
                Privacy Policy
              </button>{" "}
              and
              <button className="text-[#1d9bf0] hover:underline p-0 inline font-normal">
                {" "}
                Terms of Service
              </button>{" "}
              apply.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
